# from firecrawl import <PERSON><PERSON>raw<PERSON><PERSON>pp
import json
import os

import requests
from markdown_it import MarkdownIt

from shared.python.utils.get_aws_config import get_aws_config
from src.tasks.extraction.utils.get_extraction_config import (
    get_process_resources,
    get_scrape_config,
)
from src.tasks.extraction.utils.resource_manager import get_api_key, get_s3_bucket_name


def scrape_url(
    url,
    api_key,
    include_tags=None,
    only_main_content=True,
    formats=None,
    mock_file=None,
    save_response_to=None,
):
    """
    Send a scrape request to the Firecrawl API or use a mock file.

    Args:
        url (str): The URL to scrape
        api_key (str): The API key for authentication
        include_tags (list, optional): Tags to include in the scrape. Defaults to None.
        only_main_content (bool, optional): Whether to scrape only the main content. Defaults to True.
        formats (list, optional): Output formats. Defaults to ["markdown", "html"].
        mock_file (str, optional): Path to a file containing mock API response. If provided, API won't be called.
        save_response_to (str, optional): Path to save the API response. Defaults to None.

    Returns:
        dict: The API response data or mock data
    """
    # Use mock file if provided
    if mock_file and os.path.exists(mock_file):
        try:
            with open(mock_file, "r") as f:
                mock_data = json.load(f)
            print(f"Using mock data from {mock_file}")
            return MockResponse(mock_data)
        except Exception as e:
            print(f"Error loading mock file: {e}")
            print("Falling back to API call")

    api_endpoint = "https://api.firecrawl.dev/v1/scrape"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }

    # Set default values if not provided
    if formats is None:
        formats = ["markdown", "html"]
    if include_tags is None:
        include_tags = []

    payload = {
        "url": url,
        "formats": formats,
        "onlyMainContent": only_main_content,
    }

    # Only add includeTags if there are tags to include
    if include_tags:
        payload["includeTags"] = include_tags

    response = requests.post(api_endpoint, headers=headers, json=payload)

    # Save response to file if requested
    if save_response_to and response.status_code == 200:
        try:
            os.makedirs(os.path.dirname(save_response_to), exist_ok=True)
            with open(save_response_to, "w") as f:
                json.dump(response.json(), f, indent=2)
            print(f"Response saved to {save_response_to}")
        except Exception as e:
            print(f"Error saving response to file: {e}")

    return response


# Mock Response class to mimic requests.Response when using file-based mock
class MockResponse:
    def __init__(self, json_data, status_code=200):
        self.json_data = json_data
        self.status_code = status_code

    def json(self):
        return self.json_data


def main():
    """
    Main function to orchestrate the data extraction process.

    Returns:
        int: Exit code (0 for success)
    """
    # Get configuration
    scrape_config = get_scrape_config(
        "src/tasks/extraction/configs/extraction_basketball_reference.yaml"
    )

    aws_config = get_aws_config()
    process_name = scrape_config.get("process_name")

    # Get resources for the process
    relevant_process_resources = get_process_resources(aws_config, process_name)

    # Get API key from secrets manager
    api_key = get_api_key(relevant_process_resources)

    # Example usage with mocking and saving capabilities
    url = "https://www.basketball-reference.com/leagues/NBA_2025_games-october.html"

    # Define paths for mock file and response saving
    mock_file_path = None
    # mock_file_path = "src/tasks/extraction/example_response.json"
    mock_file_path = "src/tasks/extraction/example_response.html"
    save_response_path = "src/tasks/extraction/example_response.html"

    # Use the updated scrape_url function with new parameters
    response = scrape_url(
        url=url,
        api_key=api_key,
        include_tags=["#schedule"],
        formats=["html"],
        mock_file=mock_file_path,
        save_response_to=save_response_path,  # Save the response for future mocking
    )

    # Process the response if it contains markdown content
    if response.status_code == 200:
        response_data = response.json()

        # Get S3 bucket name for storing the extracted data
        source_bucket_name = get_s3_bucket_name(relevant_process_resources)

        # Save the results to S3
        save_endpoint_results(response_data, source_bucket_name)

    return 0


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
