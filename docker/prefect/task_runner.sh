#!/bin/bash
# docker/prefect/task_runner.sh --functions-path=../../shared/bash/functions.sh

# Get script directory
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# Parse command line arguments
FUNCTIONS_PATH=""
DETACHED_MODE=false
DOWN_ARGS=()

# Check if at least one argument is provided
if [ $# -eq 0 ]; then
    echo "ERROR: Missing required argument for functions.sh path"
    echo "Usage: $0 --functions-path=PATH [--detached] [other args...]"
    exit 1
fi

# Parse arguments
for arg in "$@"; do
    if [[ "$arg" == --functions-path=* ]]; then
        FUNCTIONS_PATH="${arg#*=}"
    elif [[ "$arg" == "--detached" ]]; then
        DETACHED_MODE=true
    else
        DOWN_ARGS+=("$arg")
    fi
done

# Validate functions path is provided
if [ -z "$FUNCTIONS_PATH" ]; then
    echo "ERROR: Missing required argument for functions.sh path"
    echo "Usage: $0 --functions-path=PATH [--detached] [other args...]"
    exit 1
fi

# Resolve functions path relative to current script
if [[ "$FUNCTIONS_PATH" != /* ]]; then
    FUNCTIONS_PATH="$SCRIPT_DIR/$FUNCTIONS_PATH"
fi

# Source shared functions
source "$FUNCTIONS_PATH" || { echo "ERROR: Failed to source shared functions from $FUNCTIONS_PATH"; exit 1; }

# Set path to docker-compose file
DOCKER_COMPOSE_FILE="$SCRIPT_DIR/docker-compose.yml"

# Check if docker-compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    handle_error "Docker compose file not found at $DOCKER_COMPOSE_FILE"
fi

# Login to ECR if needed
if [ -n "$AWS_REGION" ]; then
    echo "AWS region detected, logging into ECR..."
    AWS_ACCOUNT_ID=$(get_aws_account_id)
    ECR_URL=$(get_ecr_url "$AWS_ACCOUNT_ID")
    login_to_ecr "$ECR_URL"
fi

# Down the existing containers
echo "Stopping existing containers..."
docker-compose -f "$DOCKER_COMPOSE_FILE" down "${DOWN_ARGS[@]}"

# Start with server profile
echo "Starting containers with server profile..."
# Pass environment variables and start docker-compose
export AWS_ACCOUNT_ID="$AWS_ACCOUNT_ID"
export AWS_REGION="$AWS_REGION"
export PREFECT_DB_USER="$PREFECT_DB_USER"
export PREFECT_DB_PASSWORD="$PREFECT_DB_PASSWORD"
export PREFECT_IP="${PREFECT_IP:-localhost}"
export PREFECT_PORT="${PREFECT_PORT:-4200}"
export PREFECT_AGENT_API_URL="http://server:4200/api"

# # Check if detached mode is enabled
# if [ "$DETACHED_MODE" = true ]; then
#     echo "Running in detached mode..."
#     docker-compose -f "$DOCKER_COMPOSE_FILE" --profile server up -d
# else
#     # In non-detached mode, the first command will block until terminated
#     docker-compose -f "$DOCKER_COMPOSE_FILE" --profile server up
# fi
