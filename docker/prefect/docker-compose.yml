services:
  # Prefect Database
  database:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/prefect:database-latest
    restart: always
    expose:
      - 5432
    volumes: 
      - db:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${PREFECT_DB_USER}
      - POSTGRES_PASSWORD=${PREFECT_DB_PASSWORD}
      - POSTGRES_DB=prefect
    profiles: ["server"]

  # Prefect Server API and UI
  server:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/prefect:server-latest
    restart: always
    volumes:
      - prefect:/root/.prefect
    environment:
      - PREFECT_UI_URL=http://localhost:${PREFECT_PORT}/api
      # The url that the web app from browser should call.
      - PREFECT_API_URL=http://localhost:${PREFECT_PORT}/api
      - PREFECT_API_DATABASE_CONNECTION_URL=postgresql+asyncpg://${PREFECT_DB_USER}:${PREFECT_DB_PASSWORD}@database:5432/prefect
    ports:
      - 4200:4200
    depends_on:
      - database
    profiles: ["server"]

  # Prefect Agent
  agent:
    image: prefecthq/prefect:3.3.3-python3.11
    restart: always
    entrypoint: ["/opt/prefect/entrypoint.sh", "prefect", "agent", "start", "--pool", "my-pool"]
    environment:
      - PREFECT_API_URL=${PREFECT_AGENT_API_URL}
    depends_on:
      - server
    profiles: ["agent"]

  # # Prefect CLI to create work pool if it doesn't exist
  # cli:
  #   image: prefecthq/prefect:3.3.3-python3.11
  #   entrypoint: ["/bin/bash", "-c"]
  #   command: |
  #     "
  #     echo 'Waiting for Prefect server to be ready...'
      
  #     # More robust way to wait for server to be ready
  #     until curl -s -f ${PREFECT_API_URL}/health || [ $RETRY_COUNT -ge 30 ]; do
  #       echo 'Prefect server not ready yet, waiting...'
  #       sleep 2
  #       RETRY_COUNT=$((RETRY_COUNT+1))
  #     done
      
  #     if [ $RETRY_COUNT -ge 30 ]; then
  #       echo 'Prefect server failed to become ready in time'
  #       exit 1
  #     fi
      
  #     echo 'Prefect server is ready'
      
  #     echo 'Checking if work pool etl-ecs exists...'
  #     if ! prefect work-pool ls | grep -q 'etl-ecs'; then
  #       echo 'Creating work pool etl-ecs...'
  #       prefect work-pool create --type ecs etl-ecs
  #       echo 'Work pool etl-ecs created successfully'
  #     else
  #       echo 'Work pool etl-ecs already exists'
  #     fi
  #     "
  #   environment:
  #     - PREFECT_API_URL=${PREFECT_AGENT_API_URL}
  #     - RETRY_COUNT=0
  #   depends_on:
  #     - server
  #   profiles: ["server"]


volumes:
  prefect:
  db:
networks:
  default:
    name: prefect-network
