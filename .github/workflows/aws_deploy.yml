name: Deployment

on:
  push:
    branches:
      - dev
      - main

permissions:
  id-token: write
  contents: read

jobs:
  image_build_and_push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Determine environment and AWS account
        id: determine-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "aws_account=************" >> $GITHUB_ENV  # Replace with prod AWS account
            echo "Deploying to production environment"
          else
            echo "aws_account=************" >> $GITHUB_ENV  # Replace with dev AWS account
            echo "Deploying to development environment"
          fi

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ env.aws_account }}:role/github-to-aws-oidc
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Make script executable
        run: chmod +x ./deployment/image_export/build_and_export.sh

      - name: Compute build hash
        id: build-hash
        run: |
          HASH=$(find docker/ src/ shared/ -type f | sort | xargs sha256sum | sha256sum | cut -d ' ' -f1)
          echo "hash_key=build-hash-${{ runner.os }}-${HASH}" >> $GITHUB_OUTPUT
          echo "Build files hash: ${HASH}"

      - name: Check build cache
        id: check-build-cache
        uses: actions/cache@v3
        with:
          path: ./.build-marker
          key: ${{ steps.build-hash.outputs.hash_key }}

      - name: Run build_and_export.sh
        if: steps.check-build-cache.outputs.cache-hit != 'true'
        run: |
          ./deployment/image_export/build_and_export.sh
          touch .build-marker

  aws_deploy:
    needs: image_build_and_push
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22.14.0'
          cache: 'npm'

      - name: Install node dependencies
        run: npm ci

      - name: Add node_modules/.bin to PATH
        run: echo "$GITHUB_WORKSPACE/node_modules/.bin" >> $GITHUB_PATH

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
          cache: 'pip'

      - name: Install Python dependencies
        run: pip install -r deployment/aws_deployment/requirements.txt

      - name: Set PYTHONPATH
        run: echo "PYTHONPATH=$GITHUB_WORKSPACE/:$PYTHONPATH" >> $GITHUB_ENV

      - name: Determine environment and AWS account
        id: determine-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "aws_account=************" >> $GITHUB_ENV  # Replace with prod AWS account
          else
            echo "aws_account=************" >> $GITHUB_ENV  # Replace with dev AWS account
          fi

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ env.aws_account }}:role/github-to-aws-oidc
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Compute infrastructure deployment hash
        id: deployment-hash
        run: |
          HASH=$(find deployment/aws_deployment shared -type f -not -path "*/cdk.out/*" -not -path "*/node_modules/*" | sort | xargs sha256sum | sha256sum | cut -d ' ' -f1)
          echo "hash_key=deployment-hash-${{ runner.os }}-${HASH}" >> $GITHUB_OUTPUT
          echo "Deployment files hash: ${HASH}"

      - name: Check deployment cache
        id: check-cache
        uses: actions/cache@v3
        with:
          path: deployment/aws_deployment/.deployment-marker
          key: ${{ steps.deployment-hash.outputs.hash_key }}

      - name: Run CDK commands if changed
        if: steps.check-cache.outputs.cache-hit != 'true'
        env:
          AWS_CONFIG_PATH: ${{ github.workspace }}/${{ vars.AWS_CONFIG_PATH }}
        run: |
          cd deployment/aws_deployment
          npx cdk synth
          npx cdk deploy --all --require-approval never
          touch .deployment-marker

      - name: Update AWS Secrets
        env:
          AWS_CONFIG_PATH: ${{ github.workspace }}/${{ vars.AWS_CONFIG_PATH }}
          API_KEY_BALLDONTLIE: ${{ secrets.API_KEY_BALLDONTLIE }}
          API_KEY_FIRECRAWL: ${{ secrets.API_KEY_FIRECRAWL }}
          DAGSTER_DB_USER: ${{ secrets.DAGSTER_DB_USER }}
          DAGSTER_DB_PASSWORD: ${{ secrets.DAGSTER_DB_PASSWORD }}
        run: |
          python deployment/aws_deployment/deployment_helpers/update_secrets.py

      - name: Copy and Start Dagster Server on EC2
        env:
          AWS_CONFIG_PATH: ${{ github.workspace }}/${{ vars.AWS_CONFIG_PATH }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
          DAGSTER_DB_USER: ${{ secrets.DAGSTER_DB_USER }}
          DAGSTER_DB_PASSWORD: ${{ secrets.DAGSTER_DB_PASSWORD }}
        run: |
          # Make the script executable
          chmod +x deployment/aws_deployment/scripts/copy_and_start_orchestrator.py

          # Run the Python script to copy and start the Dagster server
          # Pass the orchestrator files config to this
          python deployment/aws_deployment/scripts/copy_and_start_orchestrator.py
