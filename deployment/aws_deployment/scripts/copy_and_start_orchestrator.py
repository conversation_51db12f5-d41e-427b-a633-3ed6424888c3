#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to copy the Orchestrator server files to an EC2 instance and start it.
This script is called from the GitHub workflow after secrets are updated.
It reuses the docker-compose.yml and task_runner.sh files from the Docker Orchestrator directory.
"""

import argparse
import base64
import os
import sys
import time

import boto3
import yaml
from botocore.exceptions import ClientError

# Add the project root to the Python path to import shared modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

from shared.python.utils.get_aws_config import get_aws_account_id, get_aws_config


def get_instance_id_from_config(orchestrator_type):
    """
    Get the EC2 instance ID from the AWS config file.

    Returns:
        The EC2 instance ID parameter name
    """
    try:
        # Get the AWS config
        aws_config = get_aws_config()

        # Get the infrastructure config
        infra_config = aws_config.get("infrastructure", {})

        # Get the stacks config
        stacks_config = infra_config.get("stacks", {})

        # Get the orchestrator stack config
        orchestrator_config = stacks_config.get(orchestrator_type, {})

        # Get the EC2 config
        ec2_config = orchestrator_config.get("ec2", {})

        # Get the EC2 instance ID
        instance_id = ec2_config.get("id")

        if not instance_id:
            print("EC2 instance ID not found in config")
            sys.exit(1)

        # Add the parameter suffix
        instance_parameter = f"{instance_id}"

        print(f"Using EC2 instance parameter: {instance_parameter}")
        return instance_parameter
    except Exception as e:
        print(f"Error getting EC2 instance ID from config: {e}")
        sys.exit(1)


def read_and_encode_file(file_path):
    """
    Read a file and encode its content in base64.

    Args:
        file_path: Path to the file to read

    Returns:
        Base64 encoded content of the file
    """
    try:
        with open(file_path, "rb") as f:
            return base64.b64encode(f.read()).decode("utf-8")
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        sys.exit(1)


def copy_files_to_instance(ssm_client, instance_id, files_to_copy):
    """
    Copy files to an EC2 instance using SSM.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        files_to_copy: List of dictionaries with 'source_path' and 'target_path'
                      e.g. [{'source_path': 'local/path.txt', 'target_path': '/remote/path.txt', 'executable': True}]

    Returns:
        The command ID of the SSM command
    """
    try:
        commands = ["mkdir -p /opt/orchestrator"]

        for file_info in files_to_copy:
            source_path = file_info["source_path"]
            target_path = file_info["target_path"]
            make_executable = file_info.get("executable", False)

            # Read and base64 encode the file content
            file_content = read_and_encode_file(source_path)

            # Create command to copy the file to the instance
            copy_cmd = f"echo {file_content} | base64 -d > {target_path}"
            commands.append(copy_cmd)

            # Make the file executable if specified
            if make_executable:
                commands.append(f"chmod +x {target_path}")

        # Send the commands to the instance
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={"commands": commands},
            Comment="Copy files to EC2 instance",
        )

        return response["Command"]["CommandId"]
    except Exception as e:
        print(f"Error copying files to instance {instance_id}: {e}")
        sys.exit(1)


def wait_for_command_completion(ssm_client, command_id, instance_id, timeout=480):
    """
    Wait for an SSM command to complete.

    Args:
        ssm_client: Boto3 SSM client
        command_id: Command ID to check
        instance_id: EC2 instance ID
        timeout: Maximum time to wait in seconds

    Returns:
        True if the command completed successfully, False otherwise
    """
    start_time = time.time()
    # Add initial delay to allow command to register
    time.sleep(5)

    while time.time() - start_time < timeout:
        try:
            response = ssm_client.get_command_invocation(
                CommandId=command_id, InstanceId=instance_id
            )

            status = response["Status"]
            if status == "Success":
                return True
            elif status in ["Failed", "Cancelled", "TimedOut"]:
                print(f"Command failed with status: {status}")
                print(
                    f"Error: {response.get('StandardErrorContent', 'No error message')}"
                )
                return False

            # Command is still in progress, wait a bit
            time.sleep(2)
        except ClientError as e:
            if "InvocationDoesNotExist" in str(e):
                # Command might not be registered yet, wait and retry
                print("Command invocation not found yet, waiting...")
                time.sleep(5)
                continue
            else:
                print(f"Error checking command status: {e}")
                return False

    print(f"Timed out waiting for command to complete after {timeout} seconds")
    return False


def check_orchestrator_server_health(ssm_client, instance_id, timeout=480):
    """
    Check if the orchestrator server is running properly by checking container status.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        timeout: Maximum time to wait in seconds

    Returns:
        True if the orchestrator server is running properly, False otherwise
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # Run a command to check if the orchestrator containers are running
            response = ssm_client.send_command(
                InstanceIds=[instance_id],
                DocumentName="AWS-RunShellScript",
                Parameters={
                    "commands": [
                        "cd /opt/orchestrator",
                        "docker-compose -f docker-compose.yml ps -q | wc -l",
                        "docker-compose -f docker-compose.yml ps",
                    ]
                },
                Comment="Check orchestrator server health",
            )

            command_id = response["Command"]["CommandId"]

            # Wait for the command to complete
            check_time = time.time()
            sleep_time = 10
            while time.time() - check_time < timeout:
                try:
                    check_response = ssm_client.get_command_invocation(
                        CommandId=command_id, InstanceId=instance_id
                    )

                    if check_response["Status"] == "Success":
                        # Check if there are running containers
                        output = check_response.get("StandardOutputContent", "")

                        # The first line should be the count of running containers
                        lines = output.strip().split("\n")
                        if lines and lines[0].strip().isdigit():
                            container_count = int(lines[0].strip())
                            if container_count > 0:
                                print(
                                    f"orchestrator server is running with {container_count} containers"
                                )
                                return True

                        # If we get here, the containers might not be fully started yet
                        break
                    elif check_response["Status"] in [
                        "Failed",
                        "Cancelled",
                        "TimedOut",
                    ]:
                        break

                    time.sleep(sleep_time)
                except Exception:
                    break

            # If we're here, either the check failed or containers aren't ready yet
            print(f"Waiting {sleep_time} for orchestrator server to start...")
            time.sleep(5)

        except Exception as e:
            print(f"Error checking orchestrator server health: {e}")
            time.sleep(5)

    print(f"Timed out waiting for orchestrator server to start after {timeout} seconds")
    return False


def start_orchestrator_server(
    ssm_client,
    instance_id,
    aws_region,
    aws_account_id,
    orchestrator_db_user,
    orchestrator_db_password,
    orchestrator_ip,
    orchestrator_type="dagster",
):
    """
    Start the Orchestrator server on an EC2 instance using SSM with task_runner.sh in detached mode.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        aws_region: AWS region
        aws_account_id: AWS account ID
        orchestrator_db_user: Orchestrator database user
        orchestrator_db_password: Orchestrator database password
        orchestrator_ip: IP address for orchestrator
        orchestrator_type: Type of orchestrator (default: dagster)

    Returns:
        The command ID of the SSM command
    """
    try:
        # Set up environment variables for the task runner
        setup_env_commands = [
            "cd /opt/orchestrator",
            f"export AWS_REGION={aws_region}",
            f"export AWS_ACCOUNT_ID={aws_account_id}",
            f"export {orchestrator_type.upper()}_DB_USER={orchestrator_db_user}",
            f"export {orchestrator_type.upper()}_DB_PASSWORD={orchestrator_db_password}",
            f"export {orchestrator_type.upper()}_IP={orchestrator_ip}",
            f"export {orchestrator_type.upper()}_PORT=3000",
            # Use task_runner.sh with the --detached flag
            "/opt/orchestrator/task_runner.sh --functions-path=/opt/orchestrator/functions.sh --detached",
        ]

        # Send the command to start the Orchestrator server
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={"commands": setup_env_commands},
            Comment="Start orchestrator server after secrets update in detached mode",
        )

        return response["Command"]["CommandId"]
    except Exception as e:
        print(f"Error starting orchestrator server on instance {instance_id}: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Copy and start orchestrator server on EC2"
    )
    parser.add_argument(
        "--config-path",
        default="deployment/aws_deployment/scripts/orchestrator_files.yaml",
        help="Path to the orchestrator files configuration YAML",
    )

    args = parser.parse_args()

    # Load the orchestrator config file
    try:
        config_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../../../", args.config_path)
        )
        with open(config_path, "r") as f:
            copy_config = yaml.safe_load(f)

        # Get orchestrator type from the config
        orchestrator_type = copy_config.get(
            "orchestrator", "dagster"
        )  # Default to "dagster" if not specified

        print(f"Using orchestrator type: {orchestrator_type}")
    except Exception as e:
        print(
            f"Error loading orchestrator type from config, defaulting to 'dagster': {e}"
        )
        orchestrator_type = "dagster"

    # Get environment variables directly as variables instead of using a dictionary
    # Get region from environment variable
    aws_region = os.environ.get("AWS_REGION")
    if not aws_region:
        print("Error: AWS_REGION environment variable not set")
        sys.exit(1)

    # Get AWS account ID
    aws_account_id = get_aws_account_id()
    if not aws_account_id:
        print("Error: AWS_ACCOUNT_ID environment variable not set")
        sys.exit(1)

    # Get orchestrator DB user
    orchestrator_db_user = os.environ.get(f"{orchestrator_type.upper()}_DB_USER")
    if not orchestrator_db_user:
        print(
            f"Error: {orchestrator_type.upper()}_DB_USER environment variable not set"
        )
        sys.exit(1)

    # Get orchestrator DB password
    orchestrator_db_password = os.environ.get(
        f"{orchestrator_type.upper()}_DB_PASSWORD"
    )
    if not orchestrator_db_password:
        print(
            f"Error: {orchestrator_type.upper()}_DB_PASSWORD environment variable not set"
        )
        sys.exit(1)

    # ORCHESTRATOR_IP will be set using the instance IP address
    orchestrator_ip = None

    # Create SSM client
    ssm_client = boto3.client("ssm", region_name=aws_region)

    # Get the EC2 instance parameter name from config
    parameter_name = get_instance_id_from_config(orchestrator_type)

    # Get the EC2 instance ID from the parameter
    try:
        response = ssm_client.get_parameter(Name=parameter_name)
        instance_id = response["Parameter"]["Value"]
        print(f"Found EC2 instance ID: {instance_id}")
    except Exception as e:
        print(f"Error getting instance ID from parameter {parameter_name}: {e}")
        sys.exit(1)

    # Get the EC2 instance IP address
    try:
        ec2_client = boto3.client("ec2", region_name=aws_region)
        response = ec2_client.describe_instances(InstanceIds=[instance_id])

        # Try to get the public IP address first
        public_ip = response["Reservations"][0]["Instances"][0].get("PublicIpAddress")

        # If no public IP, use the private IP
        if public_ip:
            orchestrator_ip = public_ip
            print(f"Using EC2 instance public IP: {public_ip}")
        else:
            private_ip = response["Reservations"][0]["Instances"][0].get(
                "PrivateIpAddress"
            )
            if private_ip:
                orchestrator_ip = private_ip
                print(
                    f"No public IP found. Using EC2 instance private IP: {private_ip}"
                )
            else:
                print("Error: Could not find any IP address for the EC2 instance")
                sys.exit(1)
    except ClientError as e:
        print(f"Error getting EC2 instance IP address: {e}")
        sys.exit(1)

    # Load the files to copy from the config file
    try:
        config_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../../../", args.config_path)
        )
        with open(config_path, "r") as f:
            copy_config = yaml.safe_load(f)

        # Get the files to copy from the config
        files_to_copy = copy_config.get("files", [])

        print(f"Using file copy configuration from {args.config_path}")
    except Exception as e:
        print(f"Error loading config file: {e}")

    # Copy the files to the instance
    print(f"Copying orchestrator files to instance {instance_id}...")
    copy_command_id = copy_files_to_instance(ssm_client, instance_id, files_to_copy)

    # Wait for the copy to complete
    print("Waiting for files copy to complete...")
    if not wait_for_command_completion(ssm_client, copy_command_id, instance_id):
        print("Failed to copy files to instance")
        sys.exit(1)

    # Start the orchestrator server
    print(f"Starting orchestrator server on instance {instance_id}...")
    start_command_id = start_orchestrator_server(
        ssm_client,
        instance_id,
        aws_region,
        aws_account_id,
        orchestrator_db_user,
        orchestrator_db_password,
        orchestrator_ip,
        orchestrator_type,
    )

    # Wait for the start command to complete
    print("Waiting for orchestrator server start command to complete...")
    if not wait_for_command_completion(
        ssm_client, start_command_id, instance_id, timeout=480
    ):
        print("Failed to execute orchestrator server start command")
        sys.exit(1)

    # Check if the orchestrator server is actually running
    print("Verifying orchestrator server is running...")
    if not check_orchestrator_server_health(ssm_client, instance_id, timeout=480):
        print("Failed to verify orchestrator server is running")
        sys.exit(1)

    print(
        f"Orchestrator server successfully started on instance {instance_id} at {orchestrator_ip}"
    )


if __name__ == "__main__":
    main()
